<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile">
        <id column="id_sop_file" property="idSopFile" />
        <result column="id_sop_main" property="idSopMain" />
        <result column="file_id" property="fileId" />
        <result column="file_url" property="fileUrl" />
        <result column="file_name" property="fileName" />
        <result column="file_format" property="fileFormat" />
        <result column="file_type" property="fileType" />
        <result column="upload_time" property="uploadTime" />
        <result column="valid_flag" property="validFlag" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_file, id_sop_main, file_id, file_url, file_name, file_format, 
        file_type, upload_time, valid_flag, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByIdSopMain" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_file
        WHERE id_sop_main = #{idSopMain}
          AND valid_flag = 'Y'
        ORDER BY sys_ctime ASC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_file
        WHERE id_sop_file = #{idSopFile}
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile">
        INSERT INTO clms_sop_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopFile}, #{idSopMain}, #{fileId}, #{fileUrl}, #{fileName}, #{fileFormat}, 
            #{fileType}, #{uploadTime}, #{validFlag}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile">
        INSERT INTO clms_sop_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopFile != null">id_sop_file,</if>
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="fileId != null">file_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileFormat != null">file_format,</if>
            <if test="fileType != null">file_type,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopFile != null">#{idSopFile},</if>
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileFormat != null">#{fileFormat},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile">
        UPDATE clms_sop_file
        <set>
            <if test="idSopMain != null">id_sop_main = #{idSopMain},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileFormat != null">file_format = #{fileFormat},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_sop_file = #{idSopFile}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile">
        UPDATE clms_sop_file
        SET id_sop_main = #{idSopMain},
            file_id = #{fileId},
            file_url = #{fileUrl},
            file_name = #{fileName},
            file_format = #{fileFormat},
            file_type = #{fileType},
            upload_time = #{uploadTime},
            valid_flag = #{validFlag},
            updated_by = #{updatedBy},
            sys_utime = #{sysUtime}
        WHERE id_sop_file = #{idSopFile}
    </update>

    <delete id="deleteByIdSopMain" parameterType="java.lang.String">
        DELETE FROM clms_sop_file
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO clms_sop_file
        (id_sop_file, id_sop_main, file_id, file_url, file_name, file_format, 
         file_type, upload_time, valid_flag, created_by, sys_ctime, updated_by, sys_utime)
        VALUES
        <foreach collection="fileList" item="item" separator=",">
            (#{item.idSopFile}, #{item.idSopMain}, #{item.fileId}, #{item.fileUrl}, 
             #{item.fileName}, #{item.fileFormat}, #{item.fileType}, #{item.uploadTime}, 
             #{item.validFlag}, #{item.createdBy}, #{item.sysCtime}, #{item.updatedBy}, #{item.sysUtime})
        </foreach>
    </insert>

    <update id="updateValidFlagByIdSopMain">
        UPDATE clms_sop_file
        SET valid_flag = #{validFlag},
            updated_by = #{updatedBy},
            sys_utime = NOW()
        WHERE id_sop_main = #{idSopMain}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM clms_sop_file
        WHERE id_sop_file = #{idSopFile}
    </delete>

</mapper>
