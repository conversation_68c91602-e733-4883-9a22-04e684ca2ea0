<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopDutyMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDuty">
        <result column="id_sop_main" property="idSopMain" />
        <result column="duty_code" property="dutyCode" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_duty, id_sop_main, duty_code, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByIdSopMain" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_duty
        WHERE id_sop_main = #{idSopMain}
        ORDER BY sys_ctime ASC
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDuty">
        INSERT INTO clms_sop_duty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopDuty}, #{idSopMain}, #{dutyCode}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopDuty">
        INSERT INTO clms_sop_duty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="dutyCode != null">duty_code,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopDuty != null">#{idSopDuty},</if>
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="dutyCode != null">#{dutyCode},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <delete id="deleteByIdSopMain" parameterType="java.lang.String">
        DELETE FROM clms_sop_duty
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO clms_sop_duty
        (id_sop_duty, id_sop_main, duty_code, created_by, sys_ctime, updated_by, sys_utime)
        VALUES
        <foreach collection="dutyList" item="item" separator=",">
            (#{item.idSopDuty}, #{item.idSopMain}, #{item.dutyCode}, #{item.createdBy}, #{item.sysCtime}, #{item.updatedBy}, #{item.sysUtime})
        </foreach>
    </insert>

    <select id="selectIdSopMainByDutyCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT id_sop_main
        FROM clms_sop_duty
        WHERE duty_code = #{dutyCode}
    </select>

</mapper>
