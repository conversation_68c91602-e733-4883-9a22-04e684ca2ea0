<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopMainMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        <id column="id_sop_main" property="idSopMain" />
        <result column="sop_name" property="sopName" />
        <result column="version_no" property="versionNo" />
        <result column="sop_description" property="sopDescription" />
        <result column="task_definition_bpm_key" property="taskDefinitionBpmKey" />
        <result column="is_all_process" property="isAllProcess" />
        <result column="is_full_replace" property="isFullReplace" />
        <result column="sop_content" property="sopContent" />
        <result column="publisher_code" property="publisherCode" />
        <result column="publisher_name" property="publisherName" />
        <result column="publish_time" property="publishTime" />
        <result column="valid_flag" property="validFlag" />
        <result column="effective_date" property="effectiveDate" />
        <result column="invalid_date" property="invalidDate" />
        <result column="status" property="status" />
        <result column="file_type" property="fileType" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_main, sop_name, version_no, sop_description, task_definition_bpm_key,
        is_all_process, is_full_replace, sop_content, publisher_code, publisher_name, 
        publish_time, valid_flag, effective_date, invalid_date, status, file_type, remark, 
        created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectSopList" resultMap="BaseResultMap">
        SELECT DISTINCT sm.*
        FROM clms_sop_main sm
        LEFT JOIN clms_sop_product sp ON sm.id_sop_main = sp.id_sop_main
        LEFT JOIN clms_sop_plan spl ON sm.id_sop_main = spl.id_sop_main
        LEFT JOIN clms_sop_duty sd ON sm.id_sop_main = sd.id_sop_main
        WHERE 1=1
        <if test="sopName != null and sopName != ''">
            AND sm.sop_name LIKE CONCAT('%', #{sopName}, '%')
        </if>
        <if test="publisherCode != null and publisherCode != ''">
            AND sm.publisher_code = #{publisherCode}
        </if>
        <if test="taskDefinitionBpmKey != null and taskDefinitionBpmKey != ''">
            AND (sm.task_definition_bpm_key LIKE CONCAT('%', #{taskDefinitionBpmKey}, '%')
                 OR sm.is_all_process = 'Y')
        </if>
        <if test="productCodes != null and productCodes.size() > 0">
            AND (sp.product_code IN
            <foreach collection="productCodes" item="productCode" open="(" separator="," close=")">
                #{productCode}
            </foreach>
            OR NOT EXISTS (SELECT 1 FROM clms_sop_product WHERE id_sop_main = sm.id_sop_main))
        </if>
        <if test="planCodes != null and planCodes.size() > 0">
            AND (spl.plan_code IN
            <foreach collection="planCodes" item="planCode" open="(" separator="," close=")">
                #{planCode}
            </foreach>
            OR NOT EXISTS (SELECT 1 FROM clms_sop_plan WHERE id_sop_main = sm.id_sop_main))
        </if>
        <if test="dutyCodes != null and dutyCodes.size() > 0">
            AND (sd.duty_code IN
            <foreach collection="dutyCodes" item="dutyCode" open="(" separator="," close=")">
                #{dutyCode}
            </foreach>
            OR NOT EXISTS (SELECT 1 FROM clms_sop_duty WHERE id_sop_main = sm.id_sop_main))
        </if>
        <if test="type != null and type != ''">
            AND sm.type = #{type}
        </if>
        <if test="status != null and status != ''">
            AND sm.status = #{status}
        </if>
        <if test="effectiveDateStart != null and effectiveDateStart != ''">
            AND sm.effective_date >= #{effectiveDateStart}
        </if>
        <if test="effectiveDateEnd != null and effectiveDateEnd != ''">
            AND sm.effective_date &lt;= #{effectiveDateEnd}
        </if>
        ORDER BY sm.sys_ctime DESC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE id_sop_main = #{idSopMain}
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        INSERT INTO clms_sop_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopMain}, #{sopName}, #{versionNo}, #{sopDescription}, #{taskDefinitionBpmKey},
            #{isAllProcess}, #{isFullReplace}, #{sopContent}, #{publisherCode}, #{publisherName}, 
            #{publishTime}, #{validFlag}, #{effectiveDate}, #{invalidDate}, #{status}, #{fileType}, 
            #{remark}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        INSERT INTO clms_sop_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="sopName != null">sop_name,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="sopDescription != null">sop_description,</if>
            <if test="taskDefinitionBpmKey != null">task_definition_bpm_key,</if>
            <if test="isAllProcess != null">is_all_process,</if>
            <if test="isFullReplace != null">is_full_replace,</if>
            <if test="sopContent != null">sop_content,</if>
            <if test="publisherCode != null">publisher_code,</if>
            <if test="publisherName != null">publisher_name,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="invalidDate != null">invalid_date,</if>
            <if test="status != null">status,</if>
            <if test="fileType != null">file_type,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="sopName != null">#{sopName},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="sopDescription != null">#{sopDescription},</if>
            <if test="taskDefinitionBpmKey != null">#{taskDefinitionBpmKey},</if>
            <if test="isAllProcess != null">#{isAllProcess},</if>
            <if test="isFullReplace != null">#{isFullReplace},</if>
            <if test="sopContent != null">#{sopContent},</if>
            <if test="publisherCode != null">#{publisherCode},</if>
            <if test="publisherName != null">#{publisherName},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="validFlag != null">#{validFlag},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="invalidDate != null">#{invalidDate},</if>
            <if test="status != null">#{status},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        UPDATE clms_sop_main
        <set>
            <if test="sopName != null">sop_name = #{sopName},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="sopDescription != null">sop_description = #{sopDescription},</if>
            <if test="taskDefinitionBpmKey != null">task_definition_bpm_key = #{taskDefinitionBpmKey},</if>
            <if test="isAllProcess != null">is_all_process = #{isAllProcess},</if>
            <if test="isFullReplace != null">is_full_replace = #{isFullReplace},</if>
            <if test="sopContent != null">sop_content = #{sopContent},</if>
            <if test="publisherCode != null">publisher_code = #{publisherCode},</if>
            <if test="publisherName != null">publisher_name = #{publisherName},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="invalidDate != null">invalid_date = #{invalidDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_sop_main = #{idSopMain}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain">
        UPDATE clms_sop_main
        SET sop_name = #{sopName},
            version_no = #{versionNo},
            sop_description = #{sopDescription},
            task_definition_bpm_key = #{taskDefinitionBpmKey},
            is_all_process = #{isAllProcess},
            is_full_replace = #{isFullReplace},
            sop_content = #{sopContent},
            publisher_code = #{publisherCode},
            publisher_name = #{publisherName},
            publish_time = #{publishTime},
            valid_flag = #{validFlag},
            effective_date = #{effectiveDate},
            invalid_date = #{invalidDate},
            status = #{status},
            file_type = #{fileType},
            remark = #{remark},
            updated_by = #{updatedBy},
            sys_utime = #{sysUtime}
        WHERE id_sop_main = #{idSopMain}
    </update>

    <select id="countValidSopByName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
          AND status = '02'
          AND valid_flag = 'Y'
        <if test="excludeId != null and excludeId != ''">
            AND id_sop_main != #{excludeId}
        </if>
    </select>

    <select id="selectVersionsBySopName" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
        ORDER BY version_no ASC
    </select>

    <select id="selectMatchingSops" resultMap="BaseResultMap">
        SELECT DISTINCT sm.*
        FROM clms_sop_main sm
        LEFT JOIN clms_sop_product sp ON sm.id_sop_main = sp.id_sop_main
        LEFT JOIN clms_sop_plan spl ON sm.id_sop_main = spl.id_sop_main
        LEFT JOIN clms_sop_duty sd ON sm.id_sop_main = sd.id_sop_main
        WHERE sm.status = '02'
          AND sm.valid_flag = 'Y'
          AND (sm.is_all_process = 'Y' 
               OR sm.task_definition_bpm_key LIKE CONCAT('%', #{taskDefinitionBpmKey}, '%'))
          AND (sp.product_code = #{productCode} 
               OR NOT EXISTS (SELECT 1 FROM clms_sop_product WHERE id_sop_main = sm.id_sop_main))
          AND (spl.plan_code = #{planCode} 
               OR NOT EXISTS (SELECT 1 FROM clms_sop_plan WHERE id_sop_main = sm.id_sop_main))
          AND (sd.duty_code = #{dutyCode} 
               OR NOT EXISTS (SELECT 1 FROM clms_sop_duty WHERE id_sop_main = sm.id_sop_main))
        ORDER BY sm.sys_ctime DESC
    </select>

    <update id="batchUpdateStatusToInvalid">
        UPDATE clms_sop_main
        SET status = '03',
            valid_flag = 'N',
            invalid_date = NOW(),
            updated_by = #{updatedBy},
            sys_utime = NOW()
        WHERE sop_name = #{sopName}
          AND status = '02'
        <if test="excludeId != null and excludeId != ''">
            AND id_sop_main != #{excludeId}
        </if>
    </update>

  
    <select id="selectSopDetailById" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.version_no AS versionNo,
            sm.sop_description AS sopDescription,
            sm.task_definition_bpm_key AS taskDefinitionBpmKey,
            sm.is_all_process AS isAllProcess,
            sm.is_full_replace AS isFullReplace,
            sm.sop_content AS sopContent,
            sm.publisher_code AS publisherCode,
            sm.publisher_name AS publisherName,
            sm.publish_time AS publishTime,
            sm.valid_flag AS validFlag,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.status AS status,
            sm.file_type AS fileType,
            sm.remark AS remark,
            sm.created_by AS createdBy,
            sm.sys_ctime AS sysCtime,
            sm.updated_by AS updatedBy,
            sm.sys_utime AS sysUtime
        FROM clms_sop_main sm
        WHERE sm.id_sop_main = #{idSopMain}
    </select>

    <select id="selectVersionListBySopName" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.vo.sop.SopVersionVO">
        SELECT
            sm.id_sop_main AS idSopMain,
            sm.version_no AS versionNo,
            sm.sop_name AS sopName,
            sm.task_definition_bpm_key AS taskDefinitionBpmKey,
            sm.publisher_name AS publisherName,
            sm.status AS status,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.sys_ctime AS sysCtime
        FROM clms_sop_main sm
        WHERE sm.sop_name = #{sopName}
        ORDER BY sm.version_no DESC
    </select>

    <select id="selectMatchingSopRules" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT DISTINCT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.version_no AS versionNo,
            sm.sop_description AS sopDescription,
            sm.task_definition_bpm_key AS taskDefinitionBpmKey,
            sm.is_all_process AS isAllProcess,
            sm.is_full_replace AS isFullReplace,
            sm.sop_content AS sopContent,
            sm.publisher_code AS publisherCode,
            sm.publisher_name AS publisherName,
            sm.publish_time AS publishTime,
            sm.valid_flag AS validFlag,
            sm.effective_date AS effectiveDate,
            sm.invalid_date AS invalidDate,
            sm.status AS status,
            sm.file_type AS fileType,
            sm.remark AS remark,
            sm.created_by AS createdBy,
            sm.sys_ctime AS sysCtime,
            sm.updated_by AS updatedBy,
            sm.sys_utime AS sysUtime
        FROM clms_sop_main sm
        WHERE sm.status = '02'
          AND sm.valid_flag = 'Y'
          AND sm.effective_date &lt;= NOW()
          AND (sm.invalid_date IS NULL OR sm.invalid_date > NOW())
          AND (
              sm.is_all_process = 'Y'
              OR (
                  #{taskDefinitionBpmKey} IS NOT NULL
                  AND sm.task_definition_bpm_key IS NOT NULL
                  AND FIND_IN_SET(#{taskDefinitionBpmKey}, sm.task_definition_bpm_key) > 0
              )
          )
          AND (
              #{productCode} IS NULL
              OR NOT EXISTS (SELECT 1 FROM clms_sop_product WHERE id_sop_main = sm.id_sop_main)
              OR EXISTS (
                  SELECT 1 FROM clms_sop_product sp
                  WHERE sp.id_sop_main = sm.id_sop_main
                  AND sp.product_code = #{productCode}
              )
          )
          AND (
              #{planCode} IS NULL
              OR NOT EXISTS (SELECT 1 FROM clms_sop_plan WHERE id_sop_main = sm.id_sop_main)
              OR EXISTS (
                  SELECT 1 FROM clms_sop_plan spl
                  WHERE spl.id_sop_main = sm.id_sop_main
                  AND spl.plan_code = #{planCode}
              )
          )
          AND (
              #{dutyCode} IS NULL
              OR NOT EXISTS (SELECT 1 FROM clms_sop_duty WHERE id_sop_main = sm.id_sop_main)
              OR EXISTS (
                  SELECT 1 FROM clms_sop_duty sd
                  WHERE sd.id_sop_main = sm.id_sop_main
                  AND sd.duty_code = #{dutyCode}
              )
          )
        ORDER BY sm.publish_time DESC, sm.sys_ctime DESC
    </select>

    <select id="selectMaxVersionBySopName" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(version_no)
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
    </select>

    <select id="countBySopName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM clms_sop_main
        WHERE sop_name = #{sopName}
        <if test="idSopMain != null and idSopMain != ''">
            AND id_sop_main != #{idSopMain}
        </if>
    </select>

    <select id="selectValidSopList" resultType="com.paic.ncbs.claim.model.vo.sop.SopMainVO">
        SELECT
            sm.id_sop_main AS idSopMain,
            sm.sop_name AS sopName,
            sm.version_no AS versionNo,
            sm.sop_description AS sopDescription,
            sm.status AS status,
            sm.type AS type
        FROM clms_sop_main sm
        WHERE sm.status = '02'
          AND sm.valid_flag = 'Y'
          AND sm.effective_date &lt;= NOW()
          AND (sm.invalid_date IS NULL OR sm.invalid_date > NOW())
        ORDER BY sm.sop_name ASC, sm.version_no DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM clms_sop_main
        WHERE id_sop_main = #{idSopMain}
    </delete>

</mapper>
