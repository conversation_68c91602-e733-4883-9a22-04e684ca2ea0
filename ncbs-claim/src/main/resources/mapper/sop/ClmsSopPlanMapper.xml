<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.sop.ClmsSopPlanMapper">

    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.sop.ClmsSopPlan">
        <result column="id_sop_main" property="idSopMain" />
        <result column="plan_code" property="planCode" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <sql id="Base_Column_List">
        id_sop_plan, id_sop_product, id_sop_main, plan_code, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByIdSopMain" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM clms_sop_plan
        WHERE id_sop_main = #{idSopMain}
        ORDER BY sys_ctime ASC
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopPlan">
        INSERT INTO clms_sop_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List" />
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{idSopPlan}, #{idSopProduct}, #{idSopMain}, #{planCode}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        </trim>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.sop.ClmsSopPlan">
        INSERT INTO clms_sop_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idSopMain != null">id_sop_main,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idSopPlan != null">#{idSopPlan},</if>
            <if test="idSopProduct != null">#{idSopProduct},</if>
            <if test="idSopMain != null">#{idSopMain},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="sysCtime != null">#{sysCtime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="sysUtime != null">#{sysUtime},</if>
        </trim>
    </insert>

    <delete id="deleteByIdSopMain" parameterType="java.lang.String">
        DELETE FROM clms_sop_plan
        WHERE id_sop_main = #{idSopMain}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO clms_sop_plan
        (id_sop_plan, id_sop_product, id_sop_main, plan_code, created_by, sys_ctime, updated_by, sys_utime)
        VALUES
        <foreach collection="planList" item="item" separator=",">
            (#{item.idSopPlan}, #{item.idSopProduct}, #{item.idSopMain}, #{item.planCode}, #{item.createdBy}, #{item.sysCtime}, #{item.updatedBy}, #{item.sysUtime})
        </foreach>
    </insert>

    <select id="selectIdSopMainByPlanCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT id_sop_main
        FROM clms_sop_plan
        WHERE plan_code = #{planCode}
    </select>

</mapper>
