create table clms_sop_main (
   id_sop_main varchar(32) not null primary key comment '主键id',
   sop_name varchar(200) comment 'sop名称',
   version_no varchar(50) comment '版本号',
   sop_description varchar(500) comment 'sop简要描述',
   task_definition_bpm_key varchar(200) comment '适用环节（逗号分隔）',
   is_all_process char(1) comment '是否全流程（Y/N）',
   is_full_replace char(1) comment '是否全文替换（Y/N）',
   sop_content longtext comment 'sop规则内容',
   publisher_code varchar(32) comment '发布人员',
   publisher_name varchar(100) comment '发布人员姓名',
   publish_time datetime comment '发布时间',
   valid_flag char(1) comment '是否有效（Y/N）',
   effective_date datetime comment '生效日期',
   invalid_date datetime comment '失效日期',
   status varchar(2) comment '状态（01-暂存、02-有效、03-无效）',
   file_type varchar(2) comment '类型（01-文本 02-文件 03-所有）',
   remark varchar(500) comment '备注',
   created_by varchar(50) not null comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) default null comment '修改人',
   sys_utime datetime not null default current_timestamp comment '修改时间',
   index idx_sop_task_definition_bpm_key  (task_definition_bpm_key),
   index idx_created_date (sys_ctime),
   index idx_updated_date (sys_utime)
);engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='sop信息表';


create table clms_sop_file (
   id_sop_file varchar(32) not null primary key comment '文件id',
   id_sop_main varchar(32) not null comment '关联sop主键',
   file_id varchar(1000) not null comment '文件id',
   file_url varchar(2000) not null comment '文件路径',
   file_name varchar(500) not null comment '文件名称',
   file_format varchar(50) not null comment '文件格式',
   file_type varchar(50) not null comment '文件类型（01-文本 02-文件 03-所有）',
   valid_flag char(1) comment '是否有效（Y/N）',
   created_by varchar(50) not null comment '创建人',
   sys_ctime datetime not null default current_timestamp comment '创建时间',
   updated_by varchar(50) default null comment '修改人',
   sys_utime datetime not null default current_timestamp comment '修改时间',
   index idx_id_sop_main (id_sop_main),
   index idx_created_date (sys_ctime),
   index idx_updated_date (sys_utime)
);engine=innodb default charset=utf8mb4 collate=utf8mb4_bin row_format=dynamic comment='sop文件表';


create table clms_sop_config (
    id_sop_config varchar(32) not null primary key comment 'SOP配置id',
    id_sop_main varchar(32) not null comment 'SOP主键ID',
    product_code varchar(50) not null comment '产品代码',
    group_code varchar(50) not null comment '方案代码',
    plan_code varchar(50) not null comment '险种代码',
    created_by varchar(50) not null comment '创建人',
    sys_ctime datetime not null default current_timestamp comment '创建时间',
    updated_by varchar(50) default null comment '修改人',
    sys_utime datetime not null default current_timestamp comment '修改时间',
    index idx_id_sop_main (id_sop_main),
    index idx_sop_product_code (product_code),
    index idx_sop_group_code (group_code),
    index idx_sop_plan_code (plan_code),
    index idx_created_date (sys_ctime),
    index idx_updated_date (sys_utime)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_bin comment='SOP配置表';

