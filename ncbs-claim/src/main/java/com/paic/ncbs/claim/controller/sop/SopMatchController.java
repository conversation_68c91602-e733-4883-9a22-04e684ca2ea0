package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.service.sop.SopMatchService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SOP匹配Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP匹配")
@RestController
@RequestMapping("/who/app/sopMatchAction")
@Slf4j
public class SopMatchController extends BaseController {

    @Autowired
    private SopMatchService sopMatchService;

    @ApiOperation("根据案件信息匹配SOP规则")
    @GetMapping(value = "/matchSopByCase")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "taskDefinitionBpmKey", value = "当前环节代码", required = false, dataType = "String", paramType = "query")
    })
    public ResponseResult<List<SopMainVO>> matchSopByCase(@RequestParam("reportNo") String reportNo,
                                                          @RequestParam(value = "caseTimes", required = false) Integer caseTimes,
                                                          @RequestParam(value = "taskDefinitionBpmKey", required = false) String taskDefinitionBpmKey) {
        try {
            LogUtil.audit("根据案件信息匹配SOP规则，reportNo：{}，caseTimes：{}，taskDefinitionBpmKey：{}", 
                    reportNo, caseTimes, taskDefinitionBpmKey);
            List<SopMainVO> result = sopMatchService.matchSopByCase(reportNo, caseTimes, taskDefinitionBpmKey);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("根据条件匹配SOP规则")
    @GetMapping(value = "/matchSopByConditions")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productCode", value = "产品代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "方案代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dutyCode", value = "险种代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "taskDefinitionBpmKey", value = "环节代码", required = false, dataType = "String", paramType = "query")
    })
    public ResponseResult<List<SopMainVO>> matchSopByConditions(@RequestParam(value = "productCode", required = false) String productCode,
                                                                @RequestParam(value = "planCode", required = false) String planCode,
                                                                @RequestParam(value = "dutyCode", required = false) String dutyCode,
                                                                @RequestParam(value = "taskDefinitionBpmKey", required = false) String taskDefinitionBpmKey) {
        try {
            LogUtil.audit("根据条件匹配SOP规则，productCode：{}，planCode：{}，dutyCode：{}，taskDefinitionBpmKey：{}", 
                    productCode, planCode, dutyCode, taskDefinitionBpmKey);
            List<SopMainVO> result = sopMatchService.matchSopByConditions(productCode, planCode, dutyCode, taskDefinitionBpmKey);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据条件匹配SOP规则失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

}
