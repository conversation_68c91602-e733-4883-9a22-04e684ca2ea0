package com.paic.ncbs.claim.service.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.dto.sop.SopQueryDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopVersionVO;
import com.paic.ncbs.claim.service.base.BaseService;

import java.util.List;

/**
 * SOP管理Service接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface SopMainService{

    /**
     * 分页查询SOP列表
     *
     * @param queryVO 查询条件
     * @return 分页结果
     */
    List<SopMainVO> getSopList(SopQueryVO queryVO);

    /**
     * 根据ID获取SOP详情
     *
     * @param idSopMain SOP主键
     * @return SOP详情
     */
    SopMainVO getSopDetail(String idSopMain);

    /**
     * 保存或更新SOP（暂存）
     *
     * @param sopMainDTO SOP信息
     * @return 操作结果
     */
    String saveOrUpdateSop(SopMainDTO sopMainDTO);

    /**
     * 发布SOP
     *
     * @param idSopMain SOP主键
     * @return 操作结果
     */
    String publishSop(String idSopMain);

    /**
     * 停用SOP
     *
     * @param idSopMain SOP主键
     * @return 操作结果
     */
    String disableSop(String idSopMain);

    /**
     * 删除SOP（暂存状态才能删除）
     *
     * @param idSopMain SOP主键
     * @return 操作结果
     */
    String deleteSop(String idSopMain);

    /**
     * 获取SOP版本列表
     *
     * @param sopName SOP名称
     * @return 版本列表
     */
    List<SopVersionVO> getSopVersionList(String sopName);

    /**
     * 根据案件信息匹配SOP规则
     *
     * @param productCode 产品代码
     * @param planCode 方案代码
     * @param dutyCode 险种代码
     * @param taskDefinitionBpmKey 环节代码
     * @return 匹配的SOP列表
     */
    List<SopMainVO> matchSopRules(String productCode, String planCode, String dutyCode, String taskDefinitionBpmKey);

    /**
     * 复制SOP（基于现有SOP创建新版本）
     *
     * @param idSopMain 源SOP主键
     * @return 新SOP主键
     */
    String copySop(String idSopMain);

    /**
     * 获取下一个版本号
     *
     * @param sopName SOP名称
     * @return 版本号
     */
    String getNextVersionNo(String sopName);

    /**
     * 校验SOP名称是否重复
     *
     * @param sopName SOP名称
     * @param idSopMain SOP主键（修改时传入，新增时为空）
     * @return 是否重复
     */
    boolean checkSopNameExists(String sopName, String idSopMain);

    /**
     * 获取有效的SOP列表（用于下拉选择）
     *
     * @return SOP列表
     */
    List<SopMainVO> getValidSopList();

}
