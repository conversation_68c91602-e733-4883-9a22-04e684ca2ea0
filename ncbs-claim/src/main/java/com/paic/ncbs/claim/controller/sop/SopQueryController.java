package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SOP查询Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP查询")
@RestController
@RequestMapping("/query/app/sopQueryAction")
@Slf4j
public class SopQueryController extends BaseController {

    @Autowired
    private SopMainService sopMainService;

    @ApiOperation("分页查询SOP列表")
    @PostMapping(value = "/getSopList")
    public ResponseResult<List<SopMainVO>> getSopList(@RequestBody SopQueryVO queryVO) {
        try {
            LogUtil.audit("分页查询SOP列表，查询条件：{}", queryVO);
            List<SopMainVO> result = sopMainService.getSopList(queryVO);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取SOP详情")
    @GetMapping(value = "/getSopDetail/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<SopMainVO> getSopDetail(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("获取SOP详情，idSopMain：{}", idSopMain);
            SopMainVO result = sopMainService.getSopDetail(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取SOP详情失败", e);
         throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("根据案件信息匹配SOP规则")
    @GetMapping(value = "/matchSopRules")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productCode", value = "产品代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "方案代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dutyCode", value = "险种代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "taskDefinitionBpmKey", value = "环节代码", required = false, dataType = "String", paramType = "query")
    })
    public ResponseResult<List<SopMainVO>> matchSopRules(@RequestParam(value = "productCode", required = false) String productCode,
                                                         @RequestParam(value = "planCode", required = false) String planCode,
                                                         @RequestParam(value = "dutyCode", required = false) String dutyCode,
                                                         @RequestParam(value = "taskDefinitionBpmKey", required = false) String taskDefinitionBpmKey) {
        try {
            LogUtil.audit("根据案件信息匹配SOP规则，productCode：{}，planCode：{}，dutyCode：{}，taskDefinitionBpmKey：{}", 
                    productCode, planCode, dutyCode, taskDefinitionBpmKey);
            List<SopMainVO> result = sopMainService.matchSopRules(productCode, planCode, dutyCode, taskDefinitionBpmKey);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
         throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取有效的SOP列表（用于下拉选择）")
    @GetMapping(value = "/getValidSopList")
    public ResponseResult<List<SopMainVO>> getValidSopList() {
        try {
            LogUtil.audit("获取有效的SOP列表");
            List<SopMainVO> result = sopMainService.getValidSopList();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取有效的SOP列表失败", e);
         throw new GlobalBusinessException(e.getMessage());
        }
    }

}
