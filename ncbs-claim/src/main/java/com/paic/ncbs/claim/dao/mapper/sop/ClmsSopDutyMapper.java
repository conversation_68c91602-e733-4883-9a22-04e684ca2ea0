package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopDuty;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP-险种关联表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopDutyMapper {

    /**
     * 根据SOP主键查询险种列表
     *
     * @param idSopMain SOP主键
     * @return 险种列表
     */
    List<ClmsSopDuty> selectByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 插入险种关联
     *
     * @param record 险种关联信息
     * @return 影响行数
     */
    int insert(ClmsSopDuty record);

    /**
     * 选择性插入险种关联
     *
     * @param record 险种关联信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopDuty record);

    /**
     * 根据SOP主键删除险种关联
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 批量插入险种关联
     *
     * @param dutyList 险种关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("dutyList") List<ClmsSopDuty> dutyList);

    /**
     * 根据险种代码查询关联的SOP主键列表
     *
     * @param dutyCode 险种代码
     * @return SOP主键列表
     */
    List<String> selectIdSopMainByDutyCode(@Param("dutyCode") String dutyCode);

}
