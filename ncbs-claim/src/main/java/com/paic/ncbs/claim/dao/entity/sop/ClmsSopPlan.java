package com.paic.ncbs.claim.dao.entity.sop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * SOP-方案关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("clms_sop_plan")
public class ClmsSopPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SOP方案id
     */
    @TableId(value = "id_sop_plan", type = IdType.INPUT)
    private String idSopPlan;

    /**
     * SOP产品id
     */
    @TableField("id_sop_product")
    private String idSopProduct;

    /**
     * SOP主键ID
     */
    @TableField("id_sop_main")
    private String idSopMain;

    /**
     * 方案代码
     */
    @TableField("plan_code")
    private String planCode;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

}
