package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopPlan;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP-方案关联表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopPlanMapper {

    /**
     * 根据SOP主键查询方案列表
     *
     * @param idSopMain SOP主键
     * @return 方案列表
     */
    List<ClmsSopPlan> selectByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 插入方案关联
     *
     * @param record 方案关联信息
     * @return 影响行数
     */
    int insert(ClmsSopPlan record);

    /**
     * 选择性插入方案关联
     *
     * @param record 方案关联信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopPlan record);

    /**
     * 根据SOP主键删除方案关联
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 批量插入方案关联
     *
     * @param planList 方案关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("planList") List<ClmsSopPlan> planList);

    /**
     * 根据方案代码查询关联的SOP主键列表
     *
     * @param planCode 方案代码
     * @return SOP主键列表
     */
    List<String> selectIdSopMainByPlanCode(@Param("planCode") String planCode);

}
