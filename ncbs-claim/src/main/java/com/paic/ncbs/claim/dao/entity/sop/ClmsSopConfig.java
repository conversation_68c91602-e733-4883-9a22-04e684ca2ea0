package com.paic.ncbs.claim.dao.entity.sop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * SOP配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Getter
@Setter
@TableName("clms_sop_config")
public class ClmsSopConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SOP配置id
     */
    @TableId(value = "id_sop_config", type = IdType.INPUT)
    private String idSopConfig;

    /**
     * SOP主键ID
     */
    @TableField("id_sop_main")
    private String idSopMain;

    /**
     * 产品代码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 方案代码
     */
    @TableField("group_code")
    private String groupCode;

    /**
     * 险种代码
     */
    @TableField("plan_code")
    private String planCode;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

}
