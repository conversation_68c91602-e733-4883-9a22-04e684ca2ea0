package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP信息表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopMainMapper {

    /**
     * 查询SOP列表
     *
     * @param sopName SOP名称
     * @param publisherCode 发布人员
     * @param taskDefinitionBpmKey 适用环节
     * @param productCodes 适用产品列表
     * @param planCodes 适用方案列表
     * @param dutyCodes 适用险种列表
     * @param type 类型
     * @param status 状态
     * @param effectiveDateStart 生效日期开始
     * @param effectiveDateEnd 生效日期结束
     * @return SOP列表
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectSopList(@Param("sopName") String sopName,
                                                                  @Param("publisherCode") String publisherCode,
                                                                  @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey,
                                                                  @Param("productCodes") List<String> productCodes,
                                                                  @Param("planCodes") List<String> planCodes,
                                                                  @Param("dutyCodes") List<String> dutyCodes,
                                                                  @Param("type") String type,
                                                                  @Param("status") String status,
                                                                  @Param("effectiveDateStart") String effectiveDateStart,
                                                                  @Param("effectiveDateEnd") String effectiveDateEnd);

    /**
     * 根据ID查询SOP
     *
     * @param idSopMain SOP主键
     * @return SOP信息
     */
    ClmsSopMain selectByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insert(ClmsSopMain record);

    /**
     * 选择性插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopMain record);

    /**
     * 选择性更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopMain record);

    /**
     * 更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopMain record);

    /**
     * 根据SOP名称查询有效的SOP数量
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int countValidSopByName(@Param("sopName") String sopName, @Param("excludeId") String excludeId);

    /**
     * 根据SOP名称查询所有版本
     *
     * @param sopName SOP名称
     * @return SOP版本列表
     */
    List<ClmsSopMain> selectVersionsBySopName(@Param("sopName") String sopName);

    /**
     * 查询匹配的SOP列表
     *
     * @param productCode 产品代码
     * @param planCode 方案代码
     * @param dutyCode 险种代码
     * @param taskDefinitionBpmKey 环节代码
     * @return 匹配的SOP列表
     */
    List<ClmsSopMain> selectMatchingSops(@Param("productCode") String productCode,
                                         @Param("planCode") String planCode,
                                         @Param("dutyCode") String dutyCode,
                                         @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    /**
     * 批量更新SOP状态为无效
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchUpdateStatusToInvalid(@Param("sopName") String sopName,
                                   @Param("excludeId") String excludeId,
                                   @Param("updatedBy") String updatedBy);

    /**
     * 根据ID查询SOP详情
     *
     * @param idSopMain SOP主键
     * @return SOP详情
     */
    com.paic.ncbs.claim.model.vo.sop.SopMainVO selectSopDetailById(@Param("idSopMain") String idSopMain);

    /**
     * 根据ID删除SOP
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 查询SOP版本列表
     *
     * @param sopName SOP名称
     * @return 版本列表
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopVersionVO> selectVersionListBySopName(@Param("sopName") String sopName);

    /**
     * 查询匹配的SOP规则
     *
     * @param productCode 产品代码
     * @param planCode 方案代码
     * @param dutyCode 险种代码
     * @param taskDefinitionBpmKey 环节代码
     * @return 匹配的SOP规则
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectMatchingSopRules(@Param("productCode") String productCode,
                                                                             @Param("planCode") String planCode,
                                                                             @Param("dutyCode") String dutyCode,
                                                                             @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    /**
     * 查询最大版本号
     *
     * @param sopName SOP名称
     * @return 最大版本号
     */
    String selectMaxVersionBySopName(@Param("sopName") String sopName);

    /**
     * 根据SOP名称查询数量
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int countBySopName(@Param("sopName") String sopName, @Param("excludeId") String excludeId);

    /**
     * 查询有效的SOP列表
     *
     * @return 有效的SOP列表
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectValidSopList();

}
