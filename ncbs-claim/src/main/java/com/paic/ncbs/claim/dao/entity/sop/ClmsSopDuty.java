package com.paic.ncbs.claim.dao.entity.sop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * SOP-险种关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("clms_sop_duty")
public class ClmsSopDuty implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SOP险种id
     */
    @TableId(value = "id_sop_duty", type = IdType.INPUT)
    private String idSopDuty;

    /**
     * SOP主键ID
     */
    @TableField("id_sop_main")
    private String idSopMain;

    /**
     * 险种代码
     */
    @TableField("duty_code")
    private String dutyCode;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

}
