package com.paic.ncbs.claim.service.sop.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.sop.*;
import com.paic.ncbs.claim.dao.mapper.sop.*;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.dto.sop.SopQueryDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopVersionVO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.sop.SopFileService;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * SOP管理Service实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMainServiceImpl implements SopMainService {

    @Autowired
    private ClmsSopMainMapper clmsSopMainMapper;

    @Autowired
    private ClmsSopFileMapper clmsSopFileMapper;

    @Autowired
    private ClmsSopProductMapper clmsSopProductMapper;

    @Autowired
    private ClmsSopPlanMapper clmsSopPlanMapper;

    @Autowired
    private ClmsSopDutyMapper clmsSopDutyMapper;

    @Autowired
    private SopFileService sopFileService;


    @Override
    public List<SopMainVO> getSopList(SopQueryVO queryVO) {
        log.info("查询SOP列表，查询条件：{}", queryVO);
        
        // 设置分页参数
        PageHelper.startPage(queryVO.getCurrentPage(), queryVO.getPageSize());
        
        // 调用查询方法
        return clmsSopMainMapper.selectSopList(queryVO.getSopName(),
                queryVO.getPublisherCode(),
                queryVO.getTaskDefinitionBpmKey(),
                queryVO.getProductCodes(),
                queryVO.getPlanCodes(),
                queryVO.getDutyCodes(),
                queryVO.getType(),
                queryVO.getStatus(),
                queryVO.getEffectiveDateStart(),
                queryVO.getEffectiveDateEnd());
    }

    @Override
    public SopMainVO getSopDetail(String idSopMain) {
        log.info("获取SOP详情，idSopMain：{}", idSopMain);
        
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");
        
        SopMainVO sopMainVO = clmsSopMainMapper.selectSopDetailById(idSopMain);
        if (sopMainVO == null) {
            throw new GlobalBusinessException("SOP不存在");
        }
        
        log.info("获取SOP详情完成，SOP名称：{}", sopMainVO.getSopName());
        return sopMainVO;
    }

    @Override
    @Transactional
    public String saveOrUpdateSop(SopMainDTO sopMainDTO) {
        log.info("保存或更新SOP，SOP名称：{}", sopMainDTO.getSopName());
        
        // 校验必填字段
        RapeStringUtils.checkIsEmpty(sopMainDTO.getSopName(), "SOP名称不能为空");
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        
        String idSopMain = sopMainDTO.getIdSopMain();
        boolean isUpdate = org.apache.commons.lang3.StringUtils.isNotEmpty(idSopMain);
        
        if (!isUpdate) {
            // 新增
            idSopMain = UuidUtil.getUUID();
            sopMainDTO.setIdSopMain(idSopMain);
            sopMainDTO.setVersionNo(getNextVersionNo(sopMainDTO.getSopName()));
            sopMainDTO.setStatus("01"); // 暂存状态
            sopMainDTO.setValidFlag("Y");
            sopMainDTO.setCreatedBy(userInfo.getUserCode());
            sopMainDTO.setSysCtime(now);
        }
        
        sopMainDTO.setUpdatedBy(userInfo.getUserCode());
        sopMainDTO.setSysUtime(now);
        
        // 转换为实体类
        ClmsSopMain entity = new ClmsSopMain();
        BeanUtils.copyProperties(sopMainDTO, entity);
        
        if (isUpdate) {
            clmsSopMainMapper.updateByPrimaryKeySelective(entity);
        } else {
            clmsSopMainMapper.insertSelective(entity);
        }
        
        // 保存关联数据
        saveAssociatedData(idSopMain, sopMainDTO, userInfo.getUserCode(), now);
        
        log.info("保存或更新SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }

    @Override
    @Transactional
    public String publishSop(String idSopMain) {
        log.info("发布SOP，idSopMain：{}", idSopMain);
        
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");
        
        ClmsSopMain entity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (entity == null) {
            throw new GlobalBusinessException("SOP不存在");
        }
        
        if (!"01".equals(entity.getStatus())) {
            throw new GlobalBusinessException("只有暂存状态的SOP才能发布");
        }
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        
        // 更新状态为有效
        entity.setStatus("02");
        entity.setPublisherCode(userInfo.getUserCode());
        entity.setPublisherName(userInfo.getUserName());
        entity.setPublishTime(now);
        entity.setEffectiveDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);
        
        clmsSopMainMapper.updateByPrimaryKeySelective(entity);
        
        log.info("发布SOP完成，idSopMain：{}", idSopMain);
        return "发布成功";
    }

    @Override
    @Transactional
    public String disableSop(String idSopMain) {
        log.info("停用SOP，idSopMain：{}", idSopMain);
        
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");
        
        ClmsSopMain entity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (entity == null) {
            throw new GlobalBusinessException("SOP不存在");
        }
        
        if (!"02".equals(entity.getStatus())) {
            throw new GlobalBusinessException("只有有效状态的SOP才能停用");
        }
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        
        // 更新状态为无效
        entity.setStatus("03");
        entity.setInvalidDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);
        
        clmsSopMainMapper.updateByPrimaryKeySelective(entity);
        
        log.info("停用SOP完成，idSopMain：{}", idSopMain);
        return "停用成功";
    }

    @Override
    @Transactional
    public String deleteSop(String idSopMain) {
        log.info("删除SOP，idSopMain：{}", idSopMain);
        
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");
        
        ClmsSopMain entity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (entity == null) {
            throw new GlobalBusinessException("SOP不存在");
        }
        
        if (!"01".equals(entity.getStatus())) {
            throw new GlobalBusinessException("只有暂存状态的SOP才能删除");
        }
        
        // 删除关联数据
        clmsSopFileMapper.deleteByIdSopMain(idSopMain);
        clmsSopProductMapper.deleteByIdSopMain(idSopMain);
        clmsSopPlanMapper.deleteByIdSopMain(idSopMain);
        clmsSopDutyMapper.deleteByIdSopMain(idSopMain);
        
        // 删除主表数据
        clmsSopMainMapper.deleteByPrimaryKey(idSopMain);
        
        log.info("删除SOP完成，idSopMain：{}", idSopMain);
        return "删除成功";
    }

    @Override
    public List<SopVersionVO> getSopVersionList(String sopName) {
        log.info("获取SOP版本列表，sopName：{}", sopName);
        
        RapeStringUtils.checkIsEmpty(sopName, "SOP名称不能为空");
        
        List<SopVersionVO> list = clmsSopMainMapper.selectVersionListBySopName(sopName);
        
        log.info("获取SOP版本列表完成，共{}个版本", list.size());
        return list;
    }

    @Override
    public List<SopMainVO> matchSopRules(String productCode, String planCode, String dutyCode, String taskDefinitionBpmKey) {
        log.info("匹配SOP规则，productCode：{}，planCode：{}，dutyCode：{}，taskDefinitionBpmKey：{}", 
                productCode, planCode, dutyCode, taskDefinitionBpmKey);
        
        List<SopMainVO> list = clmsSopMainMapper.selectMatchingSopRules(productCode, planCode, dutyCode, taskDefinitionBpmKey);
        
        log.info("匹配SOP规则完成，共匹配到{}条SOP", list.size());
        return list;
    }

    @Override
    @Transactional
    public String copySop(String idSopMain) {
        log.info("复制SOP，idSopMain：{}", idSopMain);
        
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");
        
        ClmsSopMain sourceEntity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (sourceEntity == null) {
            throw new GlobalBusinessException("源SOP不存在");
        }
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        
        // 创建新的SOP
        String newIdSopMain = UuidUtil.getUUID();
        ClmsSopMain newEntity = new ClmsSopMain();
        BeanUtils.copyProperties(sourceEntity, newEntity);
        newEntity.setIdSopMain(newIdSopMain);
        newEntity.setVersionNo(getNextVersionNo(sourceEntity.getSopName()));
        newEntity.setStatus("01"); // 暂存状态
        newEntity.setPublisherCode(null);
        newEntity.setPublisherName(null);
        newEntity.setPublishTime(null);
        newEntity.setEffectiveDate(null);
        newEntity.setInvalidDate(null);
        newEntity.setCreatedBy(userInfo.getUserCode());
        newEntity.setSysCtime(now);
        newEntity.setUpdatedBy(userInfo.getUserCode());
        newEntity.setSysUtime(now);
        
        clmsSopMainMapper.insertSelective(newEntity);
        
        // 复制关联数据
        sopFileService.copySopFiles(idSopMain, newIdSopMain);
        copyAssociatedData(idSopMain, newIdSopMain, userInfo.getUserCode(), now);
        
        log.info("复制SOP完成，新idSopMain：{}", newIdSopMain);
        return newIdSopMain;
    }

    @Override
    public String getNextVersionNo(String sopName) {
        String maxVersion = clmsSopMainMapper.selectMaxVersionBySopName(sopName);
        if (org.apache.commons.lang3.StringUtils.isEmpty((CharSequence) maxVersion)) {
            return "1.0";
        }
        
        try {
            String[] parts = maxVersion.split("\\.");
            int major = Integer.parseInt(parts[0]);
            int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            minor++;
            return major + "." + minor;
        } catch (Exception e) {
            log.warn("解析版本号失败，使用默认版本号，maxVersion：{}", maxVersion, e);
            return "1.0";
        }
    }

    @Override
    public boolean checkSopNameExists(String sopName, String idSopMain) {
        int count = clmsSopMainMapper.countBySopName(sopName, idSopMain);
        return count > 0;
    }

    @Override
    public List<SopMainVO> getValidSopList() {
        List<SopMainVO> list = clmsSopMainMapper.selectValidSopList();
        return list != null ? list : new ArrayList<>();
    }

    /**
     * 保存关联数据
     */
    private void saveAssociatedData(String idSopMain, SopMainDTO sopMainDTO, String userId, LocalDateTime now) {
        // 删除原有关联数据
        clmsSopProductMapper.deleteByIdSopMain(idSopMain);
        clmsSopPlanMapper.deleteByIdSopMain(idSopMain);
        clmsSopDutyMapper.deleteByIdSopMain(idSopMain);
        
        // 保存产品关联
        if (!RapeCollectionUtils.isEmpty(sopMainDTO.getProductCodes())) {
            List<ClmsSopProduct> productList = new ArrayList<>();
            for (String productCode : sopMainDTO.getProductCodes()) {
                ClmsSopProduct product = new ClmsSopProduct();
                product.setIdSopMain(idSopMain);
                product.setProductCode(productCode);
                product.setCreatedBy(userId);
                product.setSysCtime(now);
                product.setUpdatedBy(userId);
                product.setSysUtime(now);
                productList.add(product);
            }
            clmsSopProductMapper.batchInsert(productList);
        }
        
        // 保存方案关联
        if (!RapeCollectionUtils.isEmpty(sopMainDTO.getPlanCodes())) {
            List<ClmsSopPlan> planList = new ArrayList<>();
            for (String planCode : sopMainDTO.getPlanCodes()) {
                ClmsSopPlan plan = new ClmsSopPlan();
                plan.setIdSopMain(idSopMain);
                plan.setPlanCode(planCode);
                plan.setCreatedBy(userId);
                plan.setSysCtime(now);
                plan.setUpdatedBy(userId);
                plan.setSysUtime(now);
                planList.add(plan);
            }
            clmsSopPlanMapper.batchInsert(planList);
        }
        
        // 保存险种关联
        if (!RapeCollectionUtils.isEmpty(sopMainDTO.getDutyCodes())) {
            List<ClmsSopDuty> dutyList = new ArrayList<>();
            for (String dutyCode : sopMainDTO.getDutyCodes()) {
                ClmsSopDuty duty = new ClmsSopDuty();
                duty.setIdSopMain(idSopMain);
                duty.setDutyCode(dutyCode);
                duty.setCreatedBy(userId);
                duty.setSysCtime(now);
                duty.setUpdatedBy(userId);
                duty.setSysUtime(now);
                dutyList.add(duty);
            }
            clmsSopDutyMapper.batchInsert(dutyList);
        }
    }

    /**
     * 复制关联数据
     */
    private void copyAssociatedData(String sourceIdSopMain, String targetIdSopMain, String userId, LocalDateTime now) {
        // 复制产品关联
        List<ClmsSopProduct> sourceProducts = clmsSopProductMapper.selectByIdSopMain(sourceIdSopMain);
        if (!RapeCollectionUtils.isEmpty(sourceProducts)) {
            List<ClmsSopProduct> targetProducts = new ArrayList<>();
            for (ClmsSopProduct sourceProduct : sourceProducts) {
                ClmsSopProduct targetProduct = new ClmsSopProduct();
                BeanUtils.copyProperties(sourceProduct, targetProduct);
                targetProduct.setIdSopMain(targetIdSopMain);
                targetProduct.setCreatedBy(userId);
                targetProduct.setSysCtime(now);
                targetProduct.setUpdatedBy(userId);
                targetProduct.setSysUtime(now);
                targetProducts.add(targetProduct);
            }
            clmsSopProductMapper.batchInsert(targetProducts);
        }
        
        // 复制方案关联
        List<ClmsSopPlan> sourcePlans = clmsSopPlanMapper.selectByIdSopMain(sourceIdSopMain);
        if (!RapeCollectionUtils.isEmpty(sourcePlans)) {
            List<ClmsSopPlan> targetPlans = new ArrayList<>();
            for (ClmsSopPlan sourcePlan : sourcePlans) {
                ClmsSopPlan targetPlan = new ClmsSopPlan();
                BeanUtils.copyProperties(sourcePlan, targetPlan);
                targetPlan.setIdSopMain(targetIdSopMain);
                targetPlan.setCreatedBy(userId);
                targetPlan.setSysCtime(now);
                targetPlan.setUpdatedBy(userId);
                targetPlan.setSysUtime(now);
                targetPlans.add(targetPlan);
            }
            clmsSopPlanMapper.batchInsert(targetPlans);
        }
        
        // 复制险种关联
        List<ClmsSopDuty> sourceDuties = clmsSopDutyMapper.selectByIdSopMain(sourceIdSopMain);
        if (!RapeCollectionUtils.isEmpty(sourceDuties)) {
            List<ClmsSopDuty> targetDuties = new ArrayList<>();
            for (ClmsSopDuty sourceDuty : sourceDuties) {
                ClmsSopDuty targetDuty = new ClmsSopDuty();
                BeanUtils.copyProperties(sourceDuty, targetDuty);
                targetDuty.setIdSopMain(targetIdSopMain);
                targetDuty.setCreatedBy(userId);
                targetDuty.setSysCtime(now);
                targetDuty.setUpdatedBy(userId);
                targetDuty.setSysUtime(now);
                targetDuties.add(targetDuty);
            }
            clmsSopDutyMapper.batchInsert(targetDuties);
        }
    }

    /**
     * 获取当前用户信息
     */
    private UserInfoDTO getUserInfo() {
        return WebServletContext.getUser();
    }

}
