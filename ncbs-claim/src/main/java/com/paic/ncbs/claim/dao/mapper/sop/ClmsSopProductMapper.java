package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopProduct;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP-产品关联表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopProductMapper {

    /**
     * 根据SOP主键查询产品列表
     *
     * @param idSopMain SOP主键
     * @return 产品列表
     */
    List<ClmsSopProduct> selectByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 插入产品关联
     *
     * @param record 产品关联信息
     * @return 影响行数
     */
    int insert(ClmsSopProduct record);

    /**
     * 选择性插入产品关联
     *
     * @param record 产品关联信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopProduct record);

    /**
     * 根据SOP主键删除产品关联
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 批量插入产品关联
     *
     * @param productList 产品关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("productList") List<ClmsSopProduct> productList);

    /**
     * 根据产品代码查询关联的SOP主键列表
     *
     * @param productCode 产品代码
     * @return SOP主键列表
     */
    List<String> selectIdSopMainByProductCode(@Param("productCode") String productCode);

}
