package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopVersionVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SOP管理Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP管理")
@RestController
@RequestMapping("/mng/app/sopManageAction")
@Slf4j
public class SopManageController extends BaseController {

    @Autowired
    private SopMainService sopMainService;

    @ApiOperation("分页查询SOP列表")
    @PostMapping(value = "/getSopList")
    public ResponseResult<List<SopMainVO>> getSopList(@RequestBody SopQueryVO queryVO) {
        try {
            LogUtil.audit("分页查询SOP列表，查询条件：{}", queryVO);
            List<SopMainVO> result = sopMainService.getSopList(queryVO);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("分页查询SOP列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取SOP详情")
    @GetMapping(value = "/getSopDetail/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<SopMainVO> getSopDetail(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("获取SOP详情，idSopMain：{}", idSopMain);
            SopMainVO result = sopMainService.getSopDetail(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取SOP详情失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("保存或更新SOP（暂存）")
    @PostMapping(value = "/saveOrUpdateSop")
    public ResponseResult<String> saveOrUpdateSop(@RequestBody SopMainDTO sopMainDTO) {
        try {
            LogUtil.audit("保存或更新SOP，SOP名称：{}", sopMainDTO.getSopName());
            String result = sopMainService.saveOrUpdateSop(sopMainDTO);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("保存或更新SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("发布SOP")
    @PostMapping(value = "/publishSop/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> publishSop(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("发布SOP，idSopMain：{}", idSopMain);
            String result = sopMainService.publishSop(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("发布SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("停用SOP")
    @PostMapping(value = "/disableSop/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> disableSop(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("停用SOP，idSopMain：{}", idSopMain);
            String result = sopMainService.disableSop(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("停用SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("删除SOP")
    @DeleteMapping(value = "/deleteSop/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> deleteSop(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("删除SOP，idSopMain：{}", idSopMain);
            String result = sopMainService.deleteSop(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("删除SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取SOP版本列表")
    @GetMapping(value = "/getSopVersionList/{sopName}")
    @ApiImplicitParam(name = "sopName", value = "SOP名称", required = true, dataType = "String", paramType = "path")
    public ResponseResult<List<SopVersionVO>> getSopVersionList(@PathVariable("sopName") String sopName) {
        try {
            LogUtil.audit("获取SOP版本列表，sopName：{}", sopName);
            List<SopVersionVO> result = sopMainService.getSopVersionList(sopName);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取SOP版本列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("复制SOP")
    @PostMapping(value = "/copySop/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> copySop(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("复制SOP，idSopMain：{}", idSopMain);
            String result = sopMainService.copySop(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("复制SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("校验SOP名称是否重复")
    @GetMapping(value = "/checkSopNameExists")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sopName", value = "SOP名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idSopMain", value = "SOP主键（修改时传入）", required = false, dataType = "String", paramType = "query")
    })
    public ResponseResult<Boolean> checkSopNameExists(@RequestParam("sopName") String sopName,
                                                      @RequestParam(value = "idSopMain", required = false) String idSopMain) {
        try {
            LogUtil.audit("校验SOP名称是否重复，sopName：{}，idSopMain：{}", sopName, idSopMain);
            boolean result = sopMainService.checkSopNameExists(sopName, idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("校验SOP名称是否重复失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取有效的SOP列表（用于下拉选择）")
    @GetMapping(value = "/getValidSopList")
    public ResponseResult<List<SopMainVO>> getValidSopList() {
        try {
            LogUtil.audit("获取有效的SOP列表");
            List<SopMainVO> result = sopMainService.getValidSopList();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取有效的SOP列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取下一个版本号")
    @GetMapping(value = "/getNextVersionNo/{sopName}")
    @ApiImplicitParam(name = "sopName", value = "SOP名称", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> getNextVersionNo(@PathVariable("sopName") String sopName) {
        try {
            LogUtil.audit("获取下一个版本号，sopName：{}", sopName);
            String result = sopMainService.getNextVersionNo(sopName);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取下一个版本号失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

}
