package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP配置表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@MapperScan
public interface ClmsSopConfigMapper {

    /**
     * 根据SOP主键查询配置列表
     *
     * @param idSopMain SOP主键
     * @return 配置列表
     */
    List<ClmsSopConfig> selectByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 插入配置
     *
     * @param record 配置信息
     * @return 影响行数
     */
    int insert(ClmsSopConfig record);

    /**
     * 选择性插入配置
     *
     * @param record 配置信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopConfig record);

    /**
     * 根据SOP主键删除配置
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByIdSopMain(@Param("idSopMain") String idSopMain);

    /**
     * 批量插入配置
     *
     * @param configList 配置列表
     * @return 影响行数
     */
    int batchInsert(@Param("configList") List<ClmsSopConfig> configList);

    /**
     * 根据主键删除
     *
     * @param idSopConfig 配置主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("idSopConfig") String idSopConfig);

    /**
     * 根据主键查询
     *
     * @param idSopConfig 配置主键
     * @return 配置信息
     */
    ClmsSopConfig selectByPrimaryKey(@Param("idSopConfig") String idSopConfig);

    /**
     * 根据主键更新
     *
     * @param record 配置信息
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopConfig record);

    /**
     * 根据主键选择性更新
     *
     * @param record 配置信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopConfig record);

}
